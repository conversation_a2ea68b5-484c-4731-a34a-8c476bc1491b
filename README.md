# ARP Network Scanner

A Python-based tool to scan your local network and discover connected devices using ARP (Address Resolution Protocol) scanning techniques.

## Features

- **ARP Table Scanning**: Reads the system's ARP table for known devices
- **Network Discovery**: Performs ping sweeps to discover active devices
- **Multiple Scan Methods**: Uses `nmap` if available, falls back to ping
- **Flexible Output**: Saves results in JSON or CSV format
- **Automatic Network Detection**: Automatically detects your network ranges
- **Detailed Logging**: Timestamps and method tracking for each discovery

## Requirements

- Python 3.6+
- Linux/Unix system with standard networking tools (`arp`, `ping`, `ip`)
- Optional: `nmap` for faster and more comprehensive scanning

## Installation

1. Clone or download the files to your desired directory
2. Make the scripts executable:
   ```bash
   chmod +x arp_scanner.py scan_network.sh
   ```

## Usage

### Quick Start

Run a basic scan of your network:
```bash
./scan_network.sh
```

### Using the Shell Wrapper

The shell script provides an easy interface:

```bash
# Basic scan with JSON output
./scan_network.sh

# Save results as CSV
./scan_network.sh --format csv

# Scan specific networks
./scan_network.sh --networks ***********/24 10.0.0.0/24

# Use custom log directory
./scan_network.sh --log-dir /path/to/logs

# Show help
./scan_network.sh --help
```

### Using Python Directly

For more control, use the Python script directly:

```bash
# Basic scan
python3 arp_scanner.py

# Scan specific networks
python3 arp_scanner.py --networks ***********/24 ***********/24

# Save as CSV
python3 arp_scanner.py --format csv

# Custom log directory
python3 arp_scanner.py --log-dir /custom/path
```

## Output

The scanner provides both console output and file logging:

### Console Output
```
Found 5 devices:
--------------------------------------------------------------------------------
IP Address      MAC Address        Hostname                  Method
--------------------------------------------------------------------------------
***********     aa:bb:cc:dd:ee:ff  router.local             arp_table
***********00   11:22:33:44:55:66  laptop.local             nmap
***********50   77:88:99:aa:bb:cc  phone.local              ping_sweep
```

### Log Files

Results are saved in the `logs/` directory with timestamps:
- `arp_scan_20231201_143022.json` - JSON format
- `arp_scan_20231201_143022.csv` - CSV format

### JSON Format
```json
[
  {
    "hostname": "router.local",
    "ip": "***********",
    "mac": "aa:bb:cc:dd:ee:ff",
    "method": "arp_table",
    "timestamp": "2023-12-01T14:30:22.123456"
  }
]
```

## Scan Methods

1. **ARP Table**: Reads existing ARP entries (fastest, shows recently contacted devices)
2. **Nmap**: Uses nmap for comprehensive network discovery (requires nmap installation)
3. **Ping Sweep**: Falls back to ping-based discovery (slower but works everywhere)

## Network Detection

The scanner automatically detects your network ranges by:
- Analyzing routing table entries
- Filtering for private network ranges
- Excluding link-local addresses (169.254.x.x)

Common detected ranges:
- ***********/24
- ***********/24
- 10.0.0.0/24
- **********/16

## Permissions

Some operations may require elevated privileges:
- Reading ARP table: Usually works as regular user
- Ping sweeps: Usually works as regular user
- Nmap scans: May require sudo for some scan types

If you encounter permission issues, try running with sudo:
```bash
sudo ./scan_network.sh
```

## Troubleshooting

### Common Issues

1. **"Command not found" errors**:
   - Ensure you have `arp`, `ping`, and `ip` commands available
   - Install `nmap` for better scanning: `sudo apt install nmap` (Ubuntu/Debian)

2. **No devices found**:
   - Check if you're connected to a network
   - Try specifying networks manually with `--networks`
   - Run with sudo if permission issues are suspected

3. **Slow scanning**:
   - Install `nmap` for faster scanning
   - Reduce network range size
   - Check network connectivity

### Debug Mode

For troubleshooting, you can modify the Python script to add debug output or run individual functions.

## Security Considerations

- This tool performs network discovery which may be logged by network security systems
- Use only on networks you own or have permission to scan
- Some corporate networks may block or detect scanning activities
- The tool does not perform any intrusive operations, only standard network discovery

## License

This tool is provided as-is for educational and administrative purposes. Use responsibly and in accordance with your local laws and network policies.
