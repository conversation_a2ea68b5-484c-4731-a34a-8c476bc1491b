#!/usr/bin/env python3
"""
NFS Route Finder
Helps find and establish routes to NFS servers.
"""

import subprocess
import ipaddress
import re
import argparse
import sys

class NFSRouteFinder:
    def __init__(self):
        self.nfs_servers = []
    
    def add_nfs_server(self, ip: str, description: str = ""):
        """Add an NFS server to check."""
        try:
            ipaddress.ip_address(ip)
            self.nfs_servers.append({"ip": ip, "description": description})
        except ValueError:
            print(f"Invalid IP address: {ip}")
    
    def get_network_interfaces(self):
        """Get all network interfaces and their configurations."""
        interfaces = {}
        try:
            result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True, check=True)
            current_interface = None
            
            for line in result.stdout.split('\n'):
                # Interface line
                if re.match(r'^\d+:', line):
                    match = re.search(r'^\d+:\s+(\w+):', line)
                    if match:
                        current_interface = match.group(1)
                        interfaces[current_interface] = {
                            'name': current_interface,
                            'state': 'UP' if 'UP' in line else 'DOWN',
                            'carrier': 'NO-CARRIER' not in line,
                            'addresses': []
                        }
                
                # IP address line
                elif 'inet ' in line and current_interface:
                    match = re.search(r'inet (\d+\.\d+\.\d+\.\d+/\d+)', line)
                    if match and '127.0.0.1' not in match.group(1):
                        interfaces[current_interface]['addresses'].append(match.group(1))
        
        except subprocess.CalledProcessError as e:
            print(f"Error getting interfaces: {e}")
        
        return interfaces
    
    def get_routing_table(self):
        """Get the current routing table."""
        routes = []
        try:
            result = subprocess.run(['ip', 'route', 'show'], capture_output=True, text=True, check=True)
            for line in result.stdout.split('\n'):
                if line.strip():
                    routes.append(line.strip())
        except subprocess.CalledProcessError as e:
            print(f"Error getting routes: {e}")
        
        return routes
    
    def test_connectivity(self, ip: str):
        """Test connectivity to an IP address."""
        try:
            result = subprocess.run(['ping', '-c', '3', '-W', '2', ip], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout + result.stderr
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False, "Ping failed or timed out"
    
    def test_nfs_port(self, ip: str):
        """Test if NFS port (2049) is accessible."""
        try:
            # Try to connect to NFS port
            result = subprocess.run(['nc', '-z', '-w', '3', ip, '2049'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except subprocess.CalledProcessError:
            return False
    
    def get_network_connections(self):
        """Get NetworkManager connections."""
        connections = {}
        try:
            result = subprocess.run(['nmcli', 'connection', 'show'], 
                                  capture_output=True, text=True, check=True)
            
            for line in result.stdout.split('\n')[1:]:  # Skip header
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        name = parts[0]
                        uuid = parts[1]
                        conn_type = parts[2]
                        device = parts[3] if parts[3] != '--' else None
                        connections[name] = {
                            'uuid': uuid,
                            'type': conn_type,
                            'device': device,
                            'active': device is not None
                        }
        except subprocess.CalledProcessError as e:
            print(f"Error getting NetworkManager connections: {e}")
        
        return connections
    
    def suggest_routes(self, target_ip: str):
        """Suggest ways to reach the target IP."""
        suggestions = []
        target = ipaddress.ip_address(target_ip)
        
        # Check if target is in RFC1918 private ranges
        if target.is_private:
            suggestions.append("Target is in private IP range - likely needs direct network access")
            
            # Suggest common network configurations
            target_network = str(target).split('.')[0:3]
            target_subnet = '.'.join(target_network) + '.0/24'
            
            suggestions.append(f"Try connecting to network: {target_subnet}")
            
            # Check if we have interfaces that could be configured
            interfaces = self.get_network_interfaces()
            for name, info in interfaces.items():
                if not info['addresses'] and info['state'] == 'UP':
                    suggestions.append(f"Interface {name} is up but has no IP - could be configured for target network")
                elif not info['carrier']:
                    suggestions.append(f"Interface {name} has no carrier - check cable connection")
        
        return suggestions
    
    def analyze_nfs_servers(self):
        """Analyze connectivity to all NFS servers."""
        print("NFS Server Connectivity Analysis")
        print("=" * 50)
        
        # Get current network state
        interfaces = self.get_network_interfaces()
        routes = self.get_routing_table()
        connections = self.get_network_connections()
        
        print("\nCurrent Network Configuration:")
        print("-" * 30)
        for name, info in interfaces.items():
            if name != 'lo':  # Skip loopback
                status = "UP" if info['state'] == 'UP' else "DOWN"
                carrier = "CARRIER" if info['carrier'] else "NO-CARRIER"
                addresses = ', '.join(info['addresses']) if info['addresses'] else "No IP"
                print(f"{name}: {status}/{carrier} - {addresses}")
        
        print(f"\nRouting Table:")
        print("-" * 30)
        for route in routes:
            print(f"  {route}")
        
        print(f"\nNetworkManager Connections:")
        print("-" * 30)
        for name, info in connections.items():
            status = "ACTIVE" if info['active'] else "INACTIVE"
            device = info['device'] if info['device'] else "No device"
            print(f"  {name}: {status} ({info['type']}) - {device}")
        
        # Test each NFS server
        print(f"\nNFS Server Tests:")
        print("-" * 30)
        
        reachable_servers = []
        for server in self.nfs_servers:
            ip = server['ip']
            desc = server['description']
            
            print(f"\nTesting {ip} ({desc}):")
            
            # Test ping connectivity
            ping_success, ping_output = self.test_connectivity(ip)
            if ping_success:
                print(f"  ✓ Ping successful")
                reachable_servers.append(server)
                
                # Test NFS port
                if self.test_nfs_port(ip):
                    print(f"  ✓ NFS port (2049) accessible")
                else:
                    print(f"  ⚠ NFS port (2049) not accessible")
            else:
                print(f"  ✗ Ping failed")
                print(f"    Output: {ping_output.strip()}")
                
                # Provide suggestions
                suggestions = self.suggest_routes(ip)
                if suggestions:
                    print(f"  Suggestions:")
                    for suggestion in suggestions:
                        print(f"    - {suggestion}")
        
        # Summary
        print(f"\nSummary:")
        print("-" * 30)
        if reachable_servers:
            print(f"✓ {len(reachable_servers)} NFS server(s) reachable:")
            for server in reachable_servers:
                print(f"  - {server['ip']} ({server['description']})")
        else:
            print("✗ No NFS servers are currently reachable")
            print("\nTo establish connectivity, try:")
            print("1. Connect the appropriate network cable")
            print("2. Activate the correct NetworkManager connection")
            print("3. Configure a VPN if the servers are remote")
            print("4. Add static routes if there's a gateway to the target networks")
        
        return reachable_servers
    
    def show_nfs_mount_commands(self, reachable_servers):
        """Show NFS mount commands for reachable servers."""
        if not reachable_servers:
            return
        
        print(f"\nNFS Mount Commands:")
        print("-" * 30)
        print("To discover NFS exports on reachable servers:")
        
        for server in reachable_servers:
            ip = server['ip']
            print(f"\n# Show exports from {ip}:")
            print(f"showmount -e {ip}")
            print(f"\n# Example mount command:")
            print(f"sudo mkdir -p /mnt/nfs_{ip.replace('.', '_')}")
            print(f"sudo mount -t nfs {ip}:/path/to/export /mnt/nfs_{ip.replace('.', '_')}")

def main():
    parser = argparse.ArgumentParser(description='NFS Route Finder')
    parser.add_argument('--servers', nargs='+', help='NFS server IPs to test')
    parser.add_argument('--test-ip', help='Test connectivity to a specific IP')
    
    args = parser.parse_args()
    
    finder = NFSRouteFinder()
    
    # Add default servers if none specified
    if args.servers:
        for server in args.servers:
            finder.add_nfs_server(server, "User specified")
    else:
        finder.add_nfs_server("**************", "NFS Server 1")
        finder.add_nfs_server("*************", "NFS Server 2")
    
    if args.test_ip:
        finder.add_nfs_server(args.test_ip, "Test IP")
    
    # Analyze connectivity
    reachable = finder.analyze_nfs_servers()
    finder.show_nfs_mount_commands(reachable)

if __name__ == "__main__":
    main()
