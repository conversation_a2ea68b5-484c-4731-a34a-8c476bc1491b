#!/bin/bash

# ARP Network Scanner Wrapper Script
# Simple wrapper for the Python ARP scanner

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}ARP Network Scanner${NC}"
echo "===================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is required but not installed.${NC}"
    exit 1
fi

# Check if the scanner script exists
if [ ! -f "arp_scanner.py" ]; then
    echo -e "${RED}Error: arp_scanner.py not found in current directory.${NC}"
    exit 1
fi

# Make the Python script executable
chmod +x arp_scanner.py

# Default options
FORMAT="json"
LOG_DIR="logs"
NETWORKS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--format)
            FORMAT="$2"
            shift 2
            ;;
        -l|--log-dir)
            LOG_DIR="$2"
            shift 2
            ;;
        -n|--networks)
            shift
            while [[ $# -gt 0 && ! "$1" =~ ^- ]]; do
                NETWORKS="$NETWORKS $1"
                shift
            done
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -f, --format FORMAT     Output format (json or csv) [default: json]"
            echo "  -l, --log-dir DIR       Log directory [default: logs]"
            echo "  -n, --networks NET...   Networks to scan (e.g., ***********/24)"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Scan auto-detected networks"
            echo "  $0 -f csv                            # Save results as CSV"
            echo "  $0 -n ***********/24 10.0.0.0/24   # Scan specific networks"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Build the Python command
CMD="python3 arp_scanner.py --format $FORMAT --log-dir $LOG_DIR"
if [ ! -z "$NETWORKS" ]; then
    CMD="$CMD --networks$NETWORKS"
fi

echo -e "${YELLOW}Starting network scan...${NC}"
echo "Format: $FORMAT"
echo "Log directory: $LOG_DIR"
if [ ! -z "$NETWORKS" ]; then
    echo "Networks:$NETWORKS"
else
    echo "Networks: Auto-detected"
fi
echo ""

# Run the scanner
eval $CMD

# Check if scan was successful
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}Scan completed successfully!${NC}"
    echo "Check the '$LOG_DIR' directory for detailed logs."
else
    echo ""
    echo -e "${RED}Scan failed. Check the error messages above.${NC}"
    exit 1
fi
