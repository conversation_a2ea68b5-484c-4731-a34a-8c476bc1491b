#!/usr/bin/env python3
"""
ARP Network Scanner
Scans the local network for devices and logs the results.
"""

import subprocess
import json
import csv
import datetime
import argparse
import ipaddress
import re
import os
import sys
from typing import List, Dict, Optional

class ARPScanner:
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.ensure_log_dir()
    
    def ensure_log_dir(self):
        """Create log directory if it doesn't exist."""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def get_network_interfaces(self) -> List[str]:
        """Get active network interfaces and their IP ranges."""
        try:
            # Get network interfaces using ip command
            result = subprocess.run(['ip', 'route', 'show'], 
                                  capture_output=True, text=True, check=True)
            
            networks = []
            for line in result.stdout.split('\n'):
                if 'src' in line and '/' in line:
                    # Extract network CIDR from route output
                    parts = line.split()
                    for part in parts:
                        if '/' in part and not part.startswith('169.254'):
                            try:
                                network = ipaddress.ip_network(part, strict=False)
                                if network.is_private:
                                    networks.append(str(network))
                            except ValueError:
                                continue
            
            return list(set(networks))  # Remove duplicates
        except subprocess.CalledProcessError:
            print("Error getting network interfaces. Falling back to common ranges.")
            return ["***********/24", "***********/24", "10.0.0.0/24"]
    
    def scan_arp_table(self) -> List[Dict[str, str]]:
        """Scan the current ARP table."""
        devices = []
        try:
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, check=True)
            
            for line in result.stdout.split('\n'):
                if line.strip():
                    # Parse ARP table entries
                    # Format: hostname (ip) at mac [ether] on interface
                    match = re.search(r'(\S+)\s+\(([^)]+)\)\s+at\s+([a-fA-F0-9:]{17})', line)
                    if match:
                        hostname, ip, mac = match.groups()
                        devices.append({
                            'hostname': hostname if hostname != '?' else 'Unknown',
                            'ip': ip,
                            'mac': mac,
                            'method': 'arp_table',
                            'timestamp': datetime.datetime.now().isoformat()
                        })
        except subprocess.CalledProcessError as e:
            print(f"Error reading ARP table: {e}")
        
        return devices
    
    def ping_sweep(self, network: str) -> List[Dict[str, str]]:
        """Perform a ping sweep on the network."""
        devices = []
        try:
            net = ipaddress.ip_network(network)
            print(f"Scanning network: {network}")
            
            # Use nmap if available for faster scanning
            if self.check_nmap_available():
                return self.nmap_scan(network)
            
            # Fallback to ping sweep
            for ip in net.hosts():
                if self.ping_host(str(ip)):
                    hostname = self.get_hostname(str(ip))
                    mac = self.get_mac_address(str(ip))
                    devices.append({
                        'hostname': hostname,
                        'ip': str(ip),
                        'mac': mac,
                        'method': 'ping_sweep',
                        'timestamp': datetime.datetime.now().isoformat()
                    })
        except Exception as e:
            print(f"Error during ping sweep: {e}")
        
        return devices
    
    def check_nmap_available(self) -> bool:
        """Check if nmap is available."""
        try:
            subprocess.run(['nmap', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def nmap_scan(self, network: str) -> List[Dict[str, str]]:
        """Use nmap for network scanning."""
        devices = []
        try:
            # Use nmap for ARP scan
            result = subprocess.run([
                'nmap', '-sn', network
            ], capture_output=True, text=True, check=True)
            
            current_ip = None
            for line in result.stdout.split('\n'):
                if 'Nmap scan report for' in line:
                    # Extract IP address
                    ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
                    if ip_match:
                        current_ip = ip_match.group(1)
                        hostname = line.split('for ')[-1].strip()
                        if hostname == current_ip:
                            hostname = 'Unknown'
                elif 'MAC Address:' in line and current_ip:
                    # Extract MAC address
                    mac_match = re.search(r'([a-fA-F0-9:]{17})', line)
                    if mac_match:
                        mac = mac_match.group(1)
                        devices.append({
                            'hostname': hostname,
                            'ip': current_ip,
                            'mac': mac,
                            'method': 'nmap',
                            'timestamp': datetime.datetime.now().isoformat()
                        })
                        current_ip = None
        except subprocess.CalledProcessError as e:
            print(f"Error with nmap scan: {e}")
        
        return devices
    
    def ping_host(self, ip: str) -> bool:
        """Ping a single host."""
        try:
            result = subprocess.run([
                'ping', '-c', '1', '-W', '1', ip
            ], capture_output=True, check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def get_hostname(self, ip: str) -> str:
        """Get hostname for an IP address."""
        try:
            result = subprocess.run([
                'nslookup', ip
            ], capture_output=True, text=True)
            
            for line in result.stdout.split('\n'):
                if 'name =' in line:
                    return line.split('name = ')[-1].strip().rstrip('.')
        except:
            pass
        return 'Unknown'
    
    def get_mac_address(self, ip: str) -> str:
        """Get MAC address for an IP address from ARP table."""
        try:
            result = subprocess.run([
                'arp', '-n', ip
            ], capture_output=True, text=True)
            
            for line in result.stdout.split('\n'):
                if ip in line:
                    parts = line.split()
                    for part in parts:
                        if ':' in part and len(part) == 17:
                            return part
        except:
            pass
        return 'Unknown'
    
    def save_results(self, devices: List[Dict[str, str]], format_type: str = 'json'):
        """Save scan results to file."""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type == 'json':
            filename = f"{self.log_dir}/arp_scan_{timestamp}.json"
            with open(filename, 'w') as f:
                json.dump(devices, f, indent=2)
        elif format_type == 'csv':
            filename = f"{self.log_dir}/arp_scan_{timestamp}.csv"
            if devices:
                with open(filename, 'w', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=devices[0].keys())
                    writer.writeheader()
                    writer.writerows(devices)
        
        print(f"Results saved to: {filename}")
        return filename
    
    def print_results(self, devices: List[Dict[str, str]]):
        """Print scan results to console."""
        print(f"\nFound {len(devices)} devices:")
        print("-" * 80)
        print(f"{'IP Address':<15} {'MAC Address':<18} {'Hostname':<25} {'Method'}")
        print("-" * 80)
        
        for device in devices:
            print(f"{device['ip']:<15} {device['mac']:<18} {device['hostname']:<25} {device['method']}")
    
    def scan(self, networks: Optional[List[str]] = None, save_format: str = 'json') -> List[Dict[str, str]]:
        """Perform complete network scan."""
        all_devices = []
        
        # First, scan the ARP table
        print("Scanning ARP table...")
        arp_devices = self.scan_arp_table()
        all_devices.extend(arp_devices)
        
        # Then scan networks
        if not networks:
            networks = self.get_network_interfaces()
        
        for network in networks:
            ping_devices = self.ping_sweep(network)
            all_devices.extend(ping_devices)
        
        # Remove duplicates based on IP address
        unique_devices = {}
        for device in all_devices:
            ip = device['ip']
            if ip not in unique_devices or device['mac'] != 'Unknown':
                unique_devices[ip] = device
        
        final_devices = list(unique_devices.values())
        
        # Print and save results
        self.print_results(final_devices)
        self.save_results(final_devices, save_format)
        
        return final_devices

def main():
    parser = argparse.ArgumentParser(description='ARP Network Scanner')
    parser.add_argument('--networks', nargs='+', help='Networks to scan (e.g., ***********/24)')
    parser.add_argument('--format', choices=['json', 'csv'], default='json', help='Output format')
    parser.add_argument('--log-dir', default='logs', help='Log directory')
    
    args = parser.parse_args()
    
    scanner = ARPScanner(log_dir=args.log_dir)
    scanner.scan(networks=args.networks, save_format=args.format)

if __name__ == "__main__":
    main()
