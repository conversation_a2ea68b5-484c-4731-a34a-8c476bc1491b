#!/usr/bin/env python3
"""
NFS Mount Helper
Discovers and mounts NFS shares from specified servers.
"""

import subprocess
import os
import argparse
import json
import sys
from pathlib import Path

class NFSMountHelper:
    def __init__(self):
        self.nfs_servers = ["**************", "*************"]
        self.mount_base = "/mnt/nfs"
    
    def check_nfs_tools(self):
        """Check if required NFS tools are installed."""
        tools = ['showmount', 'mount.nfs']
        missing = []
        
        for tool in tools:
            try:
                subprocess.run(['which', tool], capture_output=True, check=True)
            except subprocess.CalledProcessError:
                missing.append(tool)
        
        if missing:
            print("Missing NFS tools. Install with:")
            print("sudo apt update && sudo apt install nfs-common")
            return False
        return True
    
    def test_connectivity(self, server):
        """Test if server is reachable."""
        try:
            result = subprocess.run(['ping', '-c', '1', '-W', '2', server], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except subprocess.CalledProcessError:
            return False
    
    def discover_exports(self, server):
        """Discover NFS exports from a server."""
        if not self.test_connectivity(server):
            print(f"❌ Server {server} is not reachable")
            return []
        
        print(f"🔍 Discovering NFS exports from {server}...")
        
        try:
            result = subprocess.run(['showmount', '-e', server], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                print(f"❌ Failed to get exports from {server}")
                print(f"Error: {result.stderr}")
                return []
            
            exports = []
            lines = result.stdout.strip().split('\n')
            
            if len(lines) <= 1:
                print(f"ℹ️  No exports found on {server}")
                return []
            
            # Skip header line
            for line in lines[1:]:
                if line.strip():
                    parts = line.split()
                    if parts:
                        export_path = parts[0]
                        clients = ' '.join(parts[1:]) if len(parts) > 1 else "everyone"
                        exports.append({
                            'server': server,
                            'path': export_path,
                            'clients': clients
                        })
            
            print(f"✅ Found {len(exports)} export(s) on {server}")
            for export in exports:
                print(f"   📁 {export['path']} (accessible to: {export['clients']})")
            
            return exports
            
        except subprocess.TimeoutExpired:
            print(f"❌ Timeout connecting to {server}")
            return []
        except subprocess.CalledProcessError as e:
            print(f"❌ Error discovering exports from {server}: {e}")
            return []
    
    def create_mount_point(self, mount_path):
        """Create mount point directory."""
        try:
            Path(mount_path).mkdir(parents=True, exist_ok=True)
            return True
        except PermissionError:
            print(f"❌ Permission denied creating {mount_path}")
            print("Try running with sudo or create the directory manually:")
            print(f"sudo mkdir -p {mount_path}")
            return False
        except Exception as e:
            print(f"❌ Error creating mount point {mount_path}: {e}")
            return False
    
    def mount_nfs(self, server, export_path, mount_point, options="rw,hard,intr"):
        """Mount an NFS export."""
        nfs_source = f"{server}:{export_path}"
        
        print(f"🔧 Mounting {nfs_source} to {mount_point}...")
        
        # Create mount point
        if not self.create_mount_point(mount_point):
            return False
        
        # Check if already mounted
        try:
            result = subprocess.run(['mount'], capture_output=True, text=True)
            if mount_point in result.stdout:
                print(f"ℹ️  {mount_point} is already mounted")
                return True
        except subprocess.CalledProcessError:
            pass
        
        # Mount the NFS share
        try:
            cmd = ['sudo', 'mount', '-t', 'nfs', '-o', options, nfs_source, mount_point]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Successfully mounted {nfs_source} to {mount_point}")
                
                # Test the mount
                try:
                    files = os.listdir(mount_point)
                    print(f"📂 Mount contains {len(files)} items")
                    if files:
                        print(f"   Sample files: {', '.join(files[:5])}")
                        if len(files) > 5:
                            print(f"   ... and {len(files) - 5} more")
                except PermissionError:
                    print("⚠️  Mount successful but no read permission")
                except Exception as e:
                    print(f"⚠️  Mount successful but error listing contents: {e}")
                
                return True
            else:
                print(f"❌ Failed to mount {nfs_source}")
                print(f"Error: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error mounting {nfs_source}: {e}")
            return False
    
    def unmount_nfs(self, mount_point):
        """Unmount an NFS share."""
        print(f"🔧 Unmounting {mount_point}...")
        
        try:
            result = subprocess.run(['sudo', 'umount', mount_point], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Successfully unmounted {mount_point}")
                return True
            else:
                print(f"❌ Failed to unmount {mount_point}")
                print(f"Error: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error unmounting {mount_point}: {e}")
            return False
    
    def list_mounted_nfs(self):
        """List currently mounted NFS shares."""
        try:
            result = subprocess.run(['mount', '-t', 'nfs'], capture_output=True, text=True)
            
            if result.stdout.strip():
                print("📋 Currently mounted NFS shares:")
                for line in result.stdout.strip().split('\n'):
                    print(f"   {line}")
            else:
                print("ℹ️  No NFS shares currently mounted")
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error listing mounted NFS shares: {e}")
    
    def interactive_mount(self):
        """Interactive NFS mounting process."""
        print("🚀 NFS Mount Helper")
        print("=" * 50)
        
        # Check tools
        if not self.check_nfs_tools():
            return
        
        # Discover exports from all servers
        all_exports = []
        for server in self.nfs_servers:
            exports = self.discover_exports(server)
            all_exports.extend(exports)
        
        if not all_exports:
            print("\n❌ No NFS exports found on any server")
            print("Servers checked:", ", ".join(self.nfs_servers))
            return
        
        print(f"\n📋 Found {len(all_exports)} total export(s):")
        for i, export in enumerate(all_exports, 1):
            print(f"{i}. {export['server']}:{export['path']} ({export['clients']})")
        
        # Let user choose
        try:
            choice = input(f"\nSelect export to mount (1-{len(all_exports)}, or 'q' to quit): ")
            
            if choice.lower() == 'q':
                print("👋 Goodbye!")
                return
            
            choice_idx = int(choice) - 1
            if choice_idx < 0 or choice_idx >= len(all_exports):
                print("❌ Invalid selection")
                return
            
            selected = all_exports[choice_idx]
            
            # Generate mount point
            server_clean = selected['server'].replace('.', '_')
            path_clean = selected['path'].replace('/', '_').strip('_')
            default_mount = f"{self.mount_base}/{server_clean}_{path_clean}"
            
            mount_point = input(f"Mount point [{default_mount}]: ").strip()
            if not mount_point:
                mount_point = default_mount
            
            # Mount options
            default_options = "rw,hard,intr"
            options = input(f"Mount options [{default_options}]: ").strip()
            if not options:
                options = default_options
            
            # Perform mount
            success = self.mount_nfs(selected['server'], selected['path'], mount_point, options)
            
            if success:
                print(f"\n🎉 NFS share mounted successfully!")
                print(f"📁 Access your files at: {mount_point}")
                print(f"\nTo unmount later, run:")
                print(f"sudo umount {mount_point}")
            
        except ValueError:
            print("❌ Invalid input")
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
    
    def auto_mount_all(self, mount_base=None):
        """Automatically mount all discovered exports."""
        if mount_base:
            self.mount_base = mount_base
        
        print("🚀 Auto-mounting all NFS exports")
        print("=" * 40)
        
        if not self.check_nfs_tools():
            return
        
        mounted_count = 0
        
        for server in self.nfs_servers:
            exports = self.discover_exports(server)
            
            for export in exports:
                server_clean = export['server'].replace('.', '_')
                path_clean = export['path'].replace('/', '_').strip('_')
                mount_point = f"{self.mount_base}/{server_clean}_{path_clean}"
                
                if self.mount_nfs(export['server'], export['path'], mount_point):
                    mounted_count += 1
        
        print(f"\n🎉 Successfully mounted {mounted_count} NFS share(s)")
        if mounted_count > 0:
            print(f"📁 Shares mounted under: {self.mount_base}")

def main():
    parser = argparse.ArgumentParser(description='NFS Mount Helper')
    parser.add_argument('--servers', nargs='+', 
                       help='NFS servers to check (default: **************, *************)')
    parser.add_argument('--mount-base', default='/mnt/nfs',
                       help='Base directory for mount points')
    parser.add_argument('--auto', action='store_true',
                       help='Automatically mount all discovered exports')
    parser.add_argument('--list', action='store_true',
                       help='List currently mounted NFS shares')
    parser.add_argument('--unmount', help='Unmount specific path')
    
    args = parser.parse_args()
    
    helper = NFSMountHelper()
    
    if args.servers:
        helper.nfs_servers = args.servers
    
    if args.mount_base:
        helper.mount_base = args.mount_base
    
    if args.list:
        helper.list_mounted_nfs()
    elif args.unmount:
        helper.unmount_nfs(args.unmount)
    elif args.auto:
        helper.auto_mount_all()
    else:
        helper.interactive_mount()

if __name__ == "__main__":
    main()
